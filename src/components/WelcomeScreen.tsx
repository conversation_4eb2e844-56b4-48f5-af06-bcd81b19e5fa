'use client'

import { useState } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Zap } from 'lucide-react'

interface WelcomeScreenProps {
  onSubmit: (input: string) => void
  loading: boolean
}

export function WelcomeScreen({ onSubmit, loading }: WelcomeScreenProps) {
  const [input, setInput] = useState('')

  const handleSubmit = () => {
    if (input.trim()) {
      onSubmit(input.trim())
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !loading) {
      handleSubmit()
    }
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-white px-4">
      {/* Logo 区域 */}
      <div className="mb-12 text-center">
        <div className="mb-6">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-50 rounded-2xl mb-4">
            <Zap className="w-8 h-8 text-blue-600" />
          </div>
        </div>
        <h1 className="text-4xl font-semibold text-gray-900 tracking-tight">
          增强阅读，即刻开始
        </h1>
        <p className="text-lg text-gray-600 mt-4 max-w-md">
          将任何网页或文章转化为结构化知识卡片，开启智能阅读体验
        </p>
      </div>

      {/* 输入区域 */}
      <div className="w-full max-w-2xl">
        <div className="relative flex items-center group">
          <Input
            placeholder="粘贴一篇文章或一个网页链接..."
            value={input}
            onChange={(e) => setInput(e.target.value)}
            onKeyDown={handleKeyDown}
            disabled={loading}
            className="h-12 pr-14 text-base border-2 bg-white focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 ease-in-out placeholder:text-gray-500"
          />
          <Button
            onClick={handleSubmit}
            disabled={!input.trim() || loading}
            size="sm"
            className="absolute right-2 h-8 w-8 p-0 transition-all duration-200 ease-in-out hover:scale-105 active:scale-95"
          >
            {loading ? (
              <div className="flex space-x-1">
                <div className="w-1 h-1 bg-current rounded-full dot-flashing"></div>
                <div className="w-1 h-1 bg-current rounded-full dot-flashing"></div>
                <div className="w-1 h-1 bg-current rounded-full dot-flashing"></div>
              </div>
            ) : (
              <Zap className="w-4 h-4" />
            )}
          </Button>
        </div>

        {/* 提示文字 */}
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-500">
            支持网页链接、文章内容或PDF文档
          </p>
        </div>
      </div>

      {/* 底部装饰 */}
      <div className="mt-16 opacity-40">
        <div className="flex items-center space-x-2 text-xs text-gray-500">
          <div className="w-2 h-2 bg-blue-600 rounded-full animate-pulse"></div>
          <span>智能解析中</span>
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" style={{animationDelay: '0.5s'}}></div>
          <span>生成卡片</span>
          <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" style={{animationDelay: '1s'}}></div>
          <span>AI问答</span>
        </div>
      </div>
    </div>
  )
} 