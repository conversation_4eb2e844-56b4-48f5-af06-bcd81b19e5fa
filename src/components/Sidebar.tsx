'use client'

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { KnowledgeCards } from './KnowledgeCards'
import { AIChat } from './AIChat'

interface KnowledgeCard {
  title: string
  content: string
}

interface ChatMessage {
  role: 'user' | 'assistant'
  content: string
}

interface SidebarProps {
  knowledgeCards: KnowledgeCard[]
  knowledgeCardsLoading: boolean
  chatMessages: ChatMessage[]
  chatLoading: boolean
  onSaveCard: (card: KnowledgeCard) => Promise<void>
  onSendMessage: (message: string) => void
}

export function Sidebar({
  knowledgeCards,
  knowledgeCardsLoading,
  chatMessages,
  chatLoading,
  onSaveCard,
  onSendMessage,
}: SidebarProps) {
  return (
    <div className="h-full bg-sidebar border-l border-border">
      <Tabs defaultValue="cards" className="h-full flex flex-col">
        {/* Tab 切换器 */}
        <div className="border-b border-border bg-sidebar">
          <TabsList className="h-12 w-full grid grid-cols-2 bg-transparent p-0">
            <TabsTrigger 
              value="cards" 
              className="h-full rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:shadow-none font-medium"
            >
              知识卡片
            </TabsTrigger>
            <TabsTrigger 
              value="chat" 
              className="h-full rounded-none border-b-2 border-transparent data-[state=active]:border-primary data-[state=active]:bg-transparent data-[state=active]:shadow-none font-medium"
            >
              AI 问答
            </TabsTrigger>
          </TabsList>
        </div>

        {/* Tab 内容区域 */}
        <div className="flex-1 overflow-hidden">
          <TabsContent 
            value="cards" 
            className="h-full m-0 overflow-y-auto scrollbar-thin p-4"
          >
            <KnowledgeCards
              cards={knowledgeCards}
              loading={knowledgeCardsLoading}
              onSaveCard={onSaveCard}
            />
          </TabsContent>

          <TabsContent 
            value="chat" 
            className="h-full m-0"
          >
            <AIChat
              messages={chatMessages}
              loading={chatLoading}
              onSendMessage={onSendMessage}
            />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  )
} 