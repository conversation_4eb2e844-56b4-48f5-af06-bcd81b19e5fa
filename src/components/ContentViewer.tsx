'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

interface ProcessedContent {
  originalContent: string
  title: string
  cached?: boolean
}

interface ContentViewerProps {
  content: ProcessedContent | null
  loading: boolean
}

function LoadingState() {
  return (
    <div className="flex flex-col items-center justify-center h-96 space-y-4">
      <div className="flex space-x-2">
        <div className="w-3 h-3 bg-primary rounded-full dot-flashing"></div>
        <div className="w-3 h-3 bg-primary rounded-full dot-flashing"></div>
        <div className="w-3 h-3 bg-primary rounded-full dot-flashing"></div>
      </div>
      <p className="text-muted-foreground">正在解析内容...</p>
    </div>
  )
}

export function ContentViewer({ content, loading }: ContentViewerProps) {
  // 如果是纯文本（不包含任何 HTML 标签），做简单的换行转换
  const formatContent = (raw: string) => {
    if (!raw) return ''
    const hasHtmlTag = /<\w+[^>]*>/.test(raw)
    if (hasHtmlTag) return raw
    // 将连续换行拆分为段落
    const paragraphs = raw.split(/\n{2,}/).map(p => p.trim()).filter(Boolean)
    return paragraphs.map(p => `<p>${p.replace(/\n/g, '<br/>')}</p>`).join('\n')
  }

  if (loading) {
    return (
      <div className="h-full bg-white rounded-lg border border-border">
        <LoadingState />
      </div>
    )
  }

  if (!content) {
    return (
      <div className="h-full bg-white rounded-lg border border-border flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="text-6xl text-muted-foreground/30">📖</div>
          <div>
            <p className="text-lg font-medium text-muted-foreground">内容将在这里显示</p>
            <p className="text-sm text-muted-foreground/60 mt-2">
              输入链接或粘贴文章开始阅读
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full bg-white">
      <Card className="h-full border-0 shadow-none">
        <CardHeader className="border-b border-border/50 bg-white sticky top-0 z-10">
          <CardTitle className="text-xl font-semibold leading-tight">
            {content.title}
          </CardTitle>
          {content.cached && (
            <CardDescription className="flex items-center space-x-2">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                ⚡ 缓存加载
              </span>
            </CardDescription>
          )}
        </CardHeader>
        <CardContent className="p-0">
          <div className="p-6 scrollbar-thin overflow-y-auto">
            <div 
              className="prose prose-slate max-w-none
                prose-headings:text-foreground prose-headings:font-semibold
                prose-p:text-foreground prose-p:leading-relaxed
                prose-a:text-primary prose-a:no-underline hover:prose-a:underline
                prose-strong:text-foreground prose-strong:font-semibold
                prose-ul:text-foreground prose-ol:text-foreground
                prose-li:text-foreground
                prose-blockquote:text-muted-foreground prose-blockquote:border-primary/20
                prose-code:text-primary prose-code:bg-muted prose-code:px-1 prose-code:py-0.5 prose-code:rounded
                prose-pre:bg-muted prose-pre:border prose-pre:border-border
                prose-img:rounded-lg prose-img:shadow-sm
                prose-hr:border-border
                [&>*:first-child]:mt-0
                [&>*:last-child]:mb-0"
              dangerouslySetInnerHTML={{ __html: formatContent(content.originalContent) }}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 