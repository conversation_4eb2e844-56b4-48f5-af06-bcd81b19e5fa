// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

// 知识卡片模型
model KnowledgeCard {
  id          String   @id @default(cuid())
  title       String
  summary     String
  content     String   // 原始内容
  keyPoints   String   // JSON 格式的关键点
  url         String   @unique
  domain      String   // 网站域名
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // 问答记录
  questions   Question[]
  
  @@map("knowledge_cards")
}

// 问答记录模型
model Question {
  id           String        @id @default(cuid())
  question     String
  answer       String
  cardId       String
  createdAt    DateTime      @default(now())
  
  // 关联知识卡片
  card         KnowledgeCard @relation(fields: [cardId], references: [id], onDelete: Cascade)
  
  @@map("questions")
}
